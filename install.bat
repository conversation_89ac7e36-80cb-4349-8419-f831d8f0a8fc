@echo off
chcp 65001
echo ========================================
echo 普通话评测系统 - 安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在安装依赖包...
pip install pyaudio numpy websocket-client pydub scipy

if errorlevel 1 (
    echo.
    echo 警告: 部分依赖包安装失败
    echo 如果pyaudio安装失败，请尝试以下方法：
    echo 1. 下载预编译的wheel文件
    echo 2. 使用conda安装: conda install pyaudio
    echo.
) else (
    echo.
    echo 依赖包安装完成！
    echo.
)

echo 正在检查音频设备...
python -c "import pyaudio; print('音频设备检查通过')" 2>nul
if errorlevel 1 (
    echo 警告: 音频设备检查失败，可能影响录音功能
    echo.
)

echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 使用方法：
echo 1. 配置科大讯飞API密钥（在程序中点击"API配置"）
echo 2. 运行程序: python main.py
echo 3. 或者双击 run.bat 启动程序
echo.
echo 如需处理MP3等格式，请安装ffmpeg：
echo https://ffmpeg.org/download.html
echo.
pause
