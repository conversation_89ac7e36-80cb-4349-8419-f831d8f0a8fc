# 普通话测评系统

一个基于深度学习的普通话发音评估系统，能够从声母、韵母、声调和流畅性四个维度对用户的发音进行评分和错误分析。

## 功能特点

- 多维度发音评估（声母、韵母、声调、流畅性）
- 智能错误检测和标注
- 可视化错误报告
- 支持MP3音频格式
- 异步处理，用户体验友好

## 技术栈

### 后端
- Python 3.8+
- Flask
- SQLAlchemy
- Celery
- librosa
- torchaudio
- transformers

### 前端
- React 18
- TypeScript
- Ant Design
- ECharts

## 项目结构

```
mandarin_evaluation/
├── backend/          # Flask后端
├── frontend/         # React前端
├── dataset/          # 数据集
└── scripts/          # 训练脚本
```

## 快速开始

### 后端启动

```bash
cd backend
pip install -r requirements.txt
python app.py
```

### 前端启动

```bash
cd frontend
npm install
npm start
```

## API文档

详见 [API文档](./backend/docs/api.md)

## 许可证

MIT