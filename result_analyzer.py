#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评测结果分析模块
"""

import json
import re
import base64
import xml.etree.ElementTree as ET
from typing import Dict, List, Any

class ResultAnalyzer:
    def __init__(self):
        """初始化结果分析器"""
        self.error_types = {
            'initial_error': '声母错误',
            'final_error': '韵母错误', 
            'tone_error': '声调错误',
            'missing': '漏读',
            'extra': '多读',
            'mispronunciation': '发音错误'
        }
    
    def analyze(self, result_data):
        """
        分析评测结果

        Args:
            result_data: 科大讯飞返回的原始结果

        Returns:
            dict: 分析后的结果
        """
        try:
            # 处理新的API返回格式
            if isinstance(result_data, dict) and 'data' in result_data:
                # 新格式：{'status': 2, 'data': 'base64编码的XML'}
                xml_data = result_data.get('data')
                if xml_data:
                    # 解码base64，尝试多种编码
                    try:
                        xml_content = base64.b64decode(xml_data).decode('utf-8')
                    except UnicodeDecodeError:
                        try:
                            xml_content = base64.b64decode(xml_data).decode('gbk')
                        except UnicodeDecodeError:
                            xml_content = base64.b64decode(xml_data).decode('utf-8', errors='ignore')
                    return self._analyze_xml_result(xml_content)
                else:
                    raise Exception("结果数据为空")
            elif isinstance(result_data, str):
                # 尝试解析为JSON（兼容旧格式）
                data = json.loads(result_data)
                return self._analyze_json_result(data)
            else:
                # 直接是字典格式
                return self._analyze_json_result(result_data)
            
        except Exception as e:
            print(f"结果分析失败: {e}")
            return self._get_default_result()

    def _analyze_xml_result(self, xml_content):
        """分析XML格式的评测结果"""
        try:
            print(f"解析XML结果: {xml_content[:200]}...")
            print(f"XML总长度: {len(xml_content)} 字符")

            # 解析XML
            root = ET.fromstring(xml_content)

            analyzed_result = {
                'total_score': 0,
                'initial_score': 0,  # 声母得分
                'final_score': 0,    # 韵母得分
                'tone_score': 0,     # 声调得分
                'fluency_score': 0,  # 流畅度得分
                'integrity_score': 0, # 完整度得分
                'annotated_text': [],
                'detailed_analysis': '',
                'error_statistics': {},
                'suggestions': []
            }

            # 查找评测结果节点
            # 可能的节点：read_syllable, read_word, read_sentence, read_chapter
            eval_node = None
            for node_name in ['read_syllable', 'read_word', 'read_sentence', 'read_chapter']:
                eval_node = root.find(f'.//{node_name}')
                if eval_node is not None:
                    break

            if eval_node is not None:
                print(f"找到评测节点: {eval_node.tag}")

                # 提取总分
                total_score = eval_node.get('total_score', '0')
                analyzed_result['total_score'] = float(total_score) if total_score else 0
                print(f"总分: {total_score}")

                # 提取各项得分
                phone_score = eval_node.get('phone_score', '0')
                tone_score = eval_node.get('tone_score', '0')
                fluency_score = eval_node.get('fluency_score', '0')
                accuracy_score = eval_node.get('accuracy_score', '0')

                analyzed_result['initial_score'] = float(phone_score) if phone_score else 0
                analyzed_result['final_score'] = float(phone_score) if phone_score else 0
                analyzed_result['tone_score'] = float(tone_score) if tone_score else 0
                analyzed_result['fluency_score'] = float(fluency_score) if fluency_score else 0

                # 如果有准确度分数，使用它作为总分的参考
                if accuracy_score and float(accuracy_score) > 0:
                    analyzed_result['total_score'] = max(analyzed_result['total_score'], float(accuracy_score))

                # 提取完整度得分（如果有）
                integrity_score = eval_node.get('integrity_score', '0')
                if integrity_score:
                    analyzed_result['integrity_score'] = float(integrity_score)

                print(f"解析得分: 总分={analyzed_result['total_score']}, 声韵={phone_score}, 声调={tone_score}")

                # 分析详细结果
                self._analyze_xml_details(root, analyzed_result)
            else:
                print("未找到评测节点，尝试查找其他节点...")
                # 打印XML结构用于调试
                for child in root:
                    print(f"根节点子元素: {child.tag} - {child.attrib}")

            return analyzed_result

        except Exception as e:
            print(f"XML解析失败: {e}")
            return self._get_default_result()

    def _analyze_json_result(self, data):
        """分析JSON格式的评测结果（兼容旧版本）"""
        analyzed_result = {
            'total_score': 0,
            'initial_score': 0,
            'final_score': 0,
            'tone_score': 0,
            'fluency_score': 0,
            'integrity_score': 0,
            'annotated_text': [],
            'detailed_analysis': '',
            'error_statistics': {},
            'suggestions': []
        }

        # 解析总体得分
        if 'total_score' in data:
            analyzed_result['total_score'] = float(data['total_score'])

        # 解析各维度得分
        if 'phone_score' in data:
            analyzed_result['initial_score'] = float(data['phone_score'])
            analyzed_result['final_score'] = float(data['phone_score'])

        if 'tone_score' in data:
            analyzed_result['tone_score'] = float(data['tone_score'])

        if 'fluency_score' in data:
            analyzed_result['fluency_score'] = float(data['fluency_score'])

        if 'integrity_score' in data:
            analyzed_result['integrity_score'] = float(data['integrity_score'])

        # 解析详细结果
        if 'words' in data:
            self._analyze_words(data['words'], analyzed_result)

        # 生成建议
        self._generate_suggestions(analyzed_result)

        return analyzed_result
    
    def _analyze_words(self, words_data, result):
        """分析词语级别的结果"""
        annotated_text = []
        detailed_analysis = []
        error_stats = {}
        
        for word in words_data:
            word_content = word.get('content', '')
            word_score = word.get('total_score', 0)
            
            detailed_analysis.append(f"\n词语: '{word_content}' - 得分: {word_score}")
            
            # 分析音节
            if 'syllables' in word:
                for syllable in word['syllables']:
                    syll_analysis = self._analyze_syllable(syllable)
                    
                    # 添加到标注文本
                    annotated_text.append({
                        'text': syll_analysis['content'],
                        'tag': syll_analysis['tag'],
                        'score': syll_analysis['score'],
                        'errors': syll_analysis['errors']
                    })
                    
                    # 添加到详细分析
                    detailed_analysis.append(f"  音节: {syll_analysis['content']}")
                    detailed_analysis.append(f"    总分: {syll_analysis['score']}")
                    
                    if syll_analysis['errors']:
                        for error in syll_analysis['errors']:
                            detailed_analysis.append(f"    {error}")
                            
                            # 统计错误类型
                            error_type = error.split(':')[0] if ':' in error else error
                            error_stats[error_type] = error_stats.get(error_type, 0) + 1
            else:
                # 如果没有音节信息，直接处理词语
                tag = self._determine_tag(word_score)
                annotated_text.append({
                    'text': word_content,
                    'tag': tag,
                    'score': word_score,
                    'errors': []
                })
        
        result['annotated_text'] = annotated_text
        result['detailed_analysis'] = '\n'.join(detailed_analysis)
        result['error_statistics'] = error_stats
    
    def _analyze_syllable(self, syllable):
        """分析单个音节"""
        content = syllable.get('content', '')
        total_score = syllable.get('total_score', 0)
        
        errors = []
        
        # 检查声母得分
        if 'initial_score' in syllable:
            initial_score = syllable['initial_score']
            if initial_score < 60:
                errors.append(f"声母错误: {initial_score}分")
        
        # 检查韵母得分
        if 'final_score' in syllable:
            final_score = syllable['final_score']
            if final_score < 60:
                errors.append(f"韵母错误: {final_score}分")
        
        # 检查声调得分
        if 'tone_score' in syllable:
            tone_score = syllable['tone_score']
            if tone_score < 60:
                errors.append(f"声调错误: {tone_score}分")
        
        # 确定标签
        tag = self._determine_tag(total_score, errors)
        
        return {
            'content': content,
            'score': total_score,
            'tag': tag,
            'errors': errors
        }
    
    def _determine_tag(self, score, errors=None):
        """根据得分和错误确定标签"""
        if errors:
            if any('声母' in error for error in errors):
                return 'error_initial'
            elif any('韵母' in error for error in errors):
                return 'error_final'
            elif any('声调' in error for error in errors):
                return 'error_tone'
            else:
                return 'error_general'
        
        if score >= 80:
            return 'correct'
        elif score >= 60:
            return 'warning'
        else:
            return 'error_general'
    
    def _generate_suggestions(self, result):
        """生成改进建议"""
        suggestions = []
        
        total_score = result['total_score']
        initial_score = result['initial_score']
        final_score = result['final_score']
        tone_score = result['tone_score']
        fluency_score = result['fluency_score']
        
        # 总体建议
        if total_score < 60:
            suggestions.append("总体发音需要大幅改进，建议加强基础练习")
        elif total_score < 80:
            suggestions.append("发音基本正确，但还有提升空间")
        else:
            suggestions.append("发音很好，继续保持")
        
        # 声母建议
        if initial_score < 70:
            suggestions.append("声母发音需要改进，注意舌位和气流控制")
        
        # 韵母建议
        if final_score < 70:
            suggestions.append("韵母发音需要改进，注意口型和舌位变化")
        
        # 声调建议
        if tone_score < 70:
            suggestions.append("声调需要改进，多练习四声的高低升降")
        
        # 流畅度建议
        if fluency_score < 70:
            suggestions.append("语音流畅度需要提高，注意语速和停顿")
        
        # 根据错误统计给出具体建议
        error_stats = result.get('error_statistics', {})
        if error_stats:
            most_common_error = max(error_stats.items(), key=lambda x: x[1])
            suggestions.append(f"最常见的问题是{most_common_error[0]}，建议重点练习")
        
        result['suggestions'] = suggestions
    
    def generate_report(self, result):
        """生成详细的评测报告"""
        report = []
        
        report.append("=" * 50)
        report.append("普通话评测报告")
        report.append("=" * 50)
        
        # 总体得分
        total_score = result.get('total_score', 0)
        report.append(f"\n总体得分: {total_score:.1f}分")
        
        # 等级评定
        if total_score >= 97:
            level = "一级甲等"
        elif total_score >= 92:
            level = "一级乙等"
        elif total_score >= 87:
            level = "二级甲等"
        elif total_score >= 80:
            level = "二级乙等"
        elif total_score >= 70:
            level = "三级甲等"
        elif total_score >= 60:
            level = "三级乙等"
        else:
            level = "不合格"
        
        report.append(f"等级评定: {level}")
        
        # 各项得分
        report.append("\n各项得分:")
        report.append(f"  声母得分: {result.get('initial_score', 0):.1f}分")
        report.append(f"  韵母得分: {result.get('final_score', 0):.1f}分")
        report.append(f"  声调得分: {result.get('tone_score', 0):.1f}分")
        report.append(f"  流畅度: {result.get('fluency_score', 0):.1f}分")
        report.append(f"  完整度: {result.get('integrity_score', 0):.1f}分")
        
        # 错误统计
        error_stats = result.get('error_statistics', {})
        if error_stats:
            report.append("\n错误统计:")
            for error_type, count in error_stats.items():
                report.append(f"  {error_type}: {count}次")
        
        # 改进建议
        suggestions = result.get('suggestions', [])
        if suggestions:
            report.append("\n改进建议:")
            for i, suggestion in enumerate(suggestions, 1):
                report.append(f"  {i}. {suggestion}")
        
        # 详细分析
        detailed_analysis = result.get('detailed_analysis', '')
        if detailed_analysis:
            report.append("\n详细分析:")
            report.append(detailed_analysis)
        
        return '\n'.join(report)

    def _analyze_xml_details(self, root, result):
        """分析XML详细结果"""
        try:
            details = []
            annotated_text = []

            # 查找所有单词节点
            words = root.findall('.//word')
            for word in words:
                word_content = word.get('content', '')
                word_score = word.get('total_score', '0')

                if word_content:
                    # 分析音节
                    syllables = word.findall('.//syll')
                    word_details = f"【{word_content}】 得分: {word_score}\n"

                    for syll in syllables:
                        syll_content = syll.get('content', '')
                        dp_message = syll.get('dp_message', '0')

                        # 判断发音问题
                        tag = 'correct'
                        if dp_message != '0':
                            if dp_message == '16':
                                tag = 'missing'
                                word_details += f"  - {syll_content}: 漏读\n"
                            elif dp_message == '32':
                                tag = 'extra'
                                word_details += f"  - {syll_content}: 增读\n"
                            elif dp_message == '128':
                                tag = 'error'
                                word_details += f"  - {syll_content}: 替换\n"

                        annotated_text.append({
                            'text': syll_content,
                            'tag': tag,
                            'score': syll.get('total_score', '0')
                        })

                    details.append(word_details)

            result['detailed_analysis'] = '\n'.join(details)
            result['annotated_text'] = annotated_text

            # 生成建议
            suggestions = []
            if result['total_score'] < 60:
                suggestions.append('建议多练习基础发音')
            elif result['total_score'] < 80:
                suggestions.append('发音基本正确，注意细节')
            else:
                suggestions.append('发音很好，继续保持')

            result['suggestions'] = suggestions

        except Exception as e:
            print(f"详细分析失败: {e}")

    def _get_default_result(self, error_msg='未知错误'):
        """获取默认结果"""
        return {
            'total_score': 0,
            'initial_score': 0,
            'final_score': 0,
            'tone_score': 0,
            'fluency_score': 0,
            'integrity_score': 0,
            'annotated_text': [],
            'detailed_analysis': error_msg,
            'error_statistics': {},
            'suggestions': ['请检查音频质量', '请确保发音清晰']
        }

    def export_to_file(self, result, filename):
        """导出结果到文件"""
        try:
            report = self.generate_report(result)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            
            return True
        except Exception as e:
            print(f"导出文件失败: {e}")
            return False

# 示例用法
if __name__ == "__main__":
    # 模拟评测结果
    mock_result = {
        "total_score": 85.5,
        "phone_score": 82.0,
        "tone_score": 88.0,
        "fluency_score": 85.0,
        "integrity_score": 90.0,
        "words": [
            {
                "content": "中华",
                "total_score": 85,
                "syllables": [
                    {
                        "content": "中",
                        "total_score": 80,
                        "initial_score": 75,
                        "final_score": 85,
                        "tone_score": 80
                    },
                    {
                        "content": "华",
                        "total_score": 90,
                        "initial_score": 95,
                        "final_score": 88,
                        "tone_score": 87
                    }
                ]
            }
        ]
    }
    
    # 分析结果
    analyzer = ResultAnalyzer()
    result = analyzer.analyze(mock_result)
    
    # 打印报告
    report = analyzer.generate_report(result)
    print(report)
    
    # 导出到文件
    analyzer.export_to_file(result, "evaluation_report.txt")
