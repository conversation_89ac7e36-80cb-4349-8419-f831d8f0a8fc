#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
科大讯飞语音评测API封装
"""

import json
import time
import hashlib
import base64
import hmac
import urllib.parse
import ssl
import websocket
import threading
from datetime import datetime, timezone
import wave
import os

class XunfeiSpeechEvaluator:
    def __init__(self, app_id, api_secret, api_key):
        self.app_id = app_id
        self.api_secret = api_secret
        self.api_key = api_key
        self.base_url = "wss://ise-api.xfyun.cn/v2/open-ise"
        self.result = None
        self.error = None
        self.finished = False
        
    def create_url(self):
        """创建WebSocket连接URL"""
        # 生成RFC1123格式的UTC时间戳
        now = datetime.now(timezone.utc)
        date = now.strftime('%a, %d %b %Y %H:%M:%S GMT')
        
        # 拼接字符串
        signature_origin = "host: ise-api.xfyun.cn\n"
        signature_origin += "date: " + date + "\n"
        signature_origin += "GET /v2/open-ise HTTP/1.1"
        
        # 进行hmac-sha256进行加密
        signature_sha = hmac.new(self.api_secret.encode('utf-8'), 
                                signature_origin.encode('utf-8'),
                                digestmod=hashlib.sha256).digest()
        signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
        
        authorization_origin = "api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
            self.api_key, "hmac-sha256", "host date request-line", signature_sha)
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
        
        # 将请求的鉴权参数组合为字典
        v = {
            "authorization": authorization,
            "date": date,
            "host": "ise-api.xfyun.cn"
        }
        
        # 拼接鉴权参数，生成url
        url = self.base_url + '?' + urllib.parse.urlencode(v)

        # 调试信息
        print(f"时间戳: {date}")
        print(f"签名原文: {signature_origin}")
        print(f"URL长度: {len(url)}")

        return url

    def evaluate(self, text, category="read_sentence", audio_file=None):
        """
        执行语音评测
        
        Args:
            text: 评测文本
            category: 评测类型 (read_syllable, read_word, read_sentence, read_chapter)
            audio_file: 音频文件路径
            
        Returns:
            tuple: (result, error)
        """
        self.result = None
        self.error = None
        self.finished = False
        
        # 构建评测参数 - 按照官方文档格式
        params = {
            "common": {
                "app_id": self.app_id
            },
            "business": {
                "sub": "ise",
                "ent": "cn_vip",
                "category": category,
                "cmd": "ssb",
                "text": text,  # 不添加BOM头，直接使用原文本
                "tte": "utf-8",
                "extra_ability": "multi_dimension",
                "ttp_skip": True,
                "aue": "raw",
                "auf": "audio/L16;rate=16000",
                "rstcd": "utf8",
                "rst": "entirety",
                "ise_unite": "1",
                "plev": "0"
            },
            "data": {
                "status": 0
            }
        }
        
        def on_message(ws, message):
            try:
                print(f"收到消息: {message[:200]}...")
                data = json.loads(message)
                if data.get('code') == 0:
                    result_data = data.get('data')
                    if result_data and result_data.get('status') == 2:
                        # 只有当status=2时才是最终结果
                        self.result = result_data
                        print("收到最终评测结果!")
                        self.finished = True
                        ws.close()
                    else:
                        print(f"收到中间结果，状态: {result_data.get('status') if result_data else 'None'}")
                else:
                    self.error = f"评测失败: {data.get('message', '未知错误')} (code: {data.get('code')})"
                    print(self.error)
                    self.finished = True
                    ws.close()
            except Exception as e:
                self.error = f"解析结果失败: {str(e)}"
                print(self.error)
                self.finished = True
                ws.close()

        def on_error(ws, error):
            self.error = f"WebSocket错误: {str(error)}"
            print(self.error)
            self.finished = True

        def on_close(ws, close_status_code, close_msg):
            print(f"连接关闭: {close_status_code}, {close_msg}")
            self.finished = True

        def on_open(ws):
            def run():
                try:
                    print("连接已建立，发送参数...")
                    # 第一步：发送参数
                    ws.send(json.dumps(params))

                    # 加载音频数据
                    if audio_file and os.path.exists(audio_file):
                        print(f"发送音频文件: {audio_file}")
                        audio_data = self._load_audio_file(audio_file)
                    else:
                        self.error = "音频文件不存在"
                        print(self.error)
                        ws.close()
                        return

                    # 第二步：分块发送音频数据
                    chunk_size = 1280
                    total_chunks = len(audio_data) // chunk_size + 1
                    print(f"音频数据大小: {len(audio_data)} 字节, 分 {total_chunks} 块发送")

                    # 分帧发送音频 - 简化版本
                    chunk_size = 1280  # 每帧1280字节
                    total_frames = (len(audio_data) + chunk_size - 1) // chunk_size
                    print(f"音频总帧数: {total_frames}")

                    for i in range(total_frames):
                        start_pos = i * chunk_size
                        end_pos = min(start_pos + chunk_size, len(audio_data))
                        chunk = audio_data[start_pos:end_pos]

                        # 确定帧类型和状态
                        if i == 0:
                            aus = 1  # 第一帧
                            status = 1
                        elif i == total_frames - 1:
                            aus = 4  # 最后一帧
                            status = 2
                        else:
                            aus = 2  # 中间帧
                            status = 1

                        audio_frame = {
                            "business": {
                                "cmd": "auw",
                                "aus": aus
                            },
                            "data": {
                                "status": status,
                                "data": base64.b64encode(chunk).decode()
                            }
                        }

                        ws.send(json.dumps(audio_frame))
                        print(f"发送第{i+1}/{total_frames}帧，aus={aus}, status={status}, 大小={len(chunk)}字节")
                        time.sleep(0.04)  # 40ms间隔

                    print("音频发送完成")
                    
                except Exception as e:
                    self.error = f"发送数据失败: {str(e)}"
                    print(self.error)
                    ws.close()
            
            thread = threading.Thread(target=run)
            thread.start()

        try:
            # 创建WebSocket连接
            url = self.create_url()
            print(f"连接到: {url[:50]}...")

            ws = websocket.WebSocketApp(url,
                                      on_message=on_message,
                                      on_error=on_error,
                                      on_close=on_close,
                                      on_open=on_open)

            ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})
            
            # 等待结果
            timeout = 30  # 30秒超时
            start_time = time.time()
            while not self.finished and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            if not self.finished:
                self.error = "评测超时"
            
        except Exception as e:
            self.error = f"连接失败: {str(e)}"
            print(f"连接异常: {e}")
        
        return self.result, self.error

    def _load_audio_file(self, audio_file):
        """加载音频文件"""
        try:
            if audio_file.endswith('.wav'):
                with wave.open(audio_file, 'rb') as wav_file:
                    # 检查音频格式
                    channels = wav_file.getnchannels()
                    sample_width = wav_file.getsampwidth()
                    framerate = wav_file.getframerate()
                    
                    print(f"音频格式: {channels}声道, {sample_width*8}bit, {framerate}Hz")
                    
                    # 读取音频数据
                    frames = wav_file.readframes(-1)
                    return frames
            else:
                # 对于其他格式，需要转换为WAV
                return self._convert_to_wav(audio_file)
                
        except Exception as e:
            raise Exception(f"加载音频文件失败: {str(e)}")

    def _convert_to_wav(self, audio_file):
        """转换音频文件为WAV格式"""
        try:
            import subprocess
            import tempfile
            
            # 创建临时WAV文件
            temp_wav = tempfile.mktemp(suffix='.wav')
            
            # 使用ffmpeg转换（需要安装ffmpeg）
            cmd = [
                'ffmpeg', '-i', audio_file,
                '-ar', '16000',  # 采样率16kHz
                '-ac', '1',      # 单声道
                '-sample_fmt', 's16',  # 16bit
                '-y', temp_wav
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"音频转换失败: {result.stderr}")
            
            # 读取转换后的文件
            with wave.open(temp_wav, 'rb') as wav_file:
                frames = wav_file.readframes(-1)
            
            # 删除临时文件
            os.unlink(temp_wav)
            
            return frames
            
        except FileNotFoundError:
            # 如果没有ffmpeg，尝试使用pydub
            try:
                from pydub import AudioSegment
                
                # 加载音频文件
                audio = AudioSegment.from_file(audio_file)
                
                # 转换为16kHz, 单声道, 16bit
                audio = audio.set_frame_rate(16000)
                audio = audio.set_channels(1)
                audio = audio.set_sample_width(2)
                
                # 导出为WAV格式的字节数据
                import io
                wav_buffer = io.BytesIO()
                audio.export(wav_buffer, format="wav")
                wav_buffer.seek(0)
                
                # 读取WAV数据（跳过WAV头部）
                wav_buffer.seek(44)  # WAV头部通常是44字节
                return wav_buffer.read()
                
            except ImportError:
                raise Exception("需要安装ffmpeg或pydub来处理非WAV格式的音频文件")

    def test_connection(self):
        """测试API连接"""
        try:
            print("测试API连接...")
            url = self.create_url()
            print(f"测试URL生成成功: {len(url)} 字符")
            return True, "URL生成成功"
        except Exception as e:
            return False, str(e)

# 示例用法
if __name__ == "__main__":
    # 测试代码
    evaluator = XunfeiSpeechEvaluator(
        app_id="your_app_id",
        api_secret="your_api_secret", 
        api_key="your_api_key"
    )
    
    # 测试连接
    success, message = evaluator.test_connection()
    print(f"连接测试: {message}")
    
    if success:
        # 测试评测
        result, error = evaluator.evaluate(
            text="中华人民共和国",
            category="read_sentence",
            audio_file="test.wav"
        )
        
        if result:
            print("评测成功:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print(f"评测失败: {error}")
