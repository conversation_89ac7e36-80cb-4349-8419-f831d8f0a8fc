#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试科大讯飞API连接
"""

import sys
import os
import json
from speech_evaluator import XunfeiSpeechEvaluator

def test_api_connection():
    """测试API连接"""
    print("=" * 50)
    print("科大讯飞API连接测试")
    print("=" * 50)
    
    # 创建评测器
    evaluator = XunfeiSpeechEvaluator(
        app_id="c94ba137",
        api_secret="OTg5ZGU2MjcxNDVkNjRkOTNiMmYyN2U2",
        api_key="98cf67ac3d06e558bd1ab6db7438acd6"
    )
    
    # 测试URL生成
    print("1. 测试URL生成...")
    try:
        url = evaluator.create_url()
        print(f"✓ URL生成成功")
        print(f"  URL长度: {len(url)} 字符")
        print(f"  URL前缀: {url[:80]}...")
    except Exception as e:
        print(f"✗ URL生成失败: {e}")
        return False
    
    # 检查音频文件
    print("\n2. 检查音频文件...")
    audio_dir = "音频"
    if os.path.exists(audio_dir):
        audio_files = [f for f in os.listdir(audio_dir) if f.lower().endswith(('.mp3', '.wav', '.m4a'))]
        if audio_files:
            test_audio = os.path.join(audio_dir, audio_files[0])
            print(f"✓ 找到测试音频: {test_audio}")
            
            # 测试音频转换
            print("\n3. 测试音频处理...")
            try:
                from audio_converter import AudioConverter
                converter = AudioConverter()
                
                # 获取音频信息
                info = converter.get_audio_info(test_audio)
                if 'error' not in info:
                    print(f"✓ 音频信息获取成功:")
                    for key, value in info.items():
                        print(f"    {key}: {value}")
                    
                    # 转换音频格式
                    print("\n4. 测试音频格式转换...")
                    converted_file = converter.convert_to_standard_format(test_audio)
                    if converted_file:
                        print(f"✓ 音频转换成功: {os.path.basename(converted_file)}")
                        
                        # 测试实际评测（简单测试）
                        print("\n5. 测试API评测...")
                        result, error = evaluator.evaluate(
                            text="测试",
                            category="read_syllable",
                            audio_file=converted_file
                        )
                        
                        if result:
                            print("✓ API评测成功!")
                            print(f"  结果: {str(result)[:100]}...")
                            return True
                        else:
                            print(f"✗ API评测失败: {error}")
                            return False
                    else:
                        print("✗ 音频转换失败")
                        return False
                else:
                    print(f"✗ 音频信息获取失败: {info['error']}")
                    return False
                    
            except ImportError as e:
                print(f"✗ 缺少依赖: {e}")
                print("请运行: pip install pydub")
                return False
            except Exception as e:
                print(f"✗ 音频处理失败: {e}")
                return False
        else:
            print("✗ 音频目录中没有找到音频文件")
            return False
    else:
        print("✗ 音频目录不存在")
        return False

def main():
    """主函数"""
    try:
        success = test_api_connection()
        
        print("\n" + "=" * 50)
        if success:
            print("✓ 所有测试通过！系统可以正常使用")
            print("\n启动主程序:")
            print("python main.py")
        else:
            print("✗ 测试失败，请检查相关问题")
            print("\n可能的解决方案:")
            print("1. 检查网络连接")
            print("2. 确认API密钥正确")
            print("3. 安装缺少的依赖: pip install pydub")
            print("4. 确保音频文件存在")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现异常: {e}")

if __name__ == "__main__":
    main()
