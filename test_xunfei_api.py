#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
科大讯飞普通话评测API测试脚本
支持四种题型：读字、读词、读句、自由说话
"""

import json
import time
import hashlib
import base64
import hmac
import urllib.parse
import urllib.request
import ssl
import websocket
import threading
from datetime import datetime
import wave
import pyaudio

class XunfeiSpeechEvaluator:
    def __init__(self, app_id, api_secret, api_key):
        self.app_id = app_id
        self.api_secret = api_secret
        self.api_key = api_key
        self.base_url = "wss://ise-api.xfyun.cn/v2/open-ise"
        
    def create_url(self):
        """创建WebSocket连接URL"""
        # 生成RFC1123格式的时间戳
        now = datetime.now()
        date = now.strftime('%a, %d %b %Y %H:%M:%S GMT')
        
        # 拼接字符串
        signature_origin = "host: ise-api.xfyun.cn\n"
        signature_origin += "date: " + date + "\n"
        signature_origin += "GET /v2/open-ise HTTP/1.1"
        
        # 进行hmac-sha256进行加密
        signature_sha = hmac.new(self.api_secret.encode('utf-8'), 
                                signature_origin.encode('utf-8'),
                                digestmod=hashlib.sha256).digest()
        signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
        
        authorization_origin = "api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
            self.api_key, "hmac-sha256", "host date request-line", signature_sha)
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
        
        # 将请求的鉴权参数组合为字典
        v = {
            "authorization": authorization,
            "date": date,
            "host": "ise-api.xfyun.cn"
        }
        
        # 拼接鉴权参数，生成url
        url = self.base_url + '?' + urllib.parse.urlencode(v)
        return url

    def test_single_word(self, text="中", audio_file=None):
        """测试单字评测"""
        print(f"\n=== 单字评测测试 ===")
        print(f"测试文本: {text}")
        
        params = {
            "app_id": self.app_id,
            "category": "read_syllable",  # 单字
            "language": "zh_cn",
            "text_encoding": "utf-8",
            "result_level": "complete",
            "plev": 0,  # 全维度评测
            "text": text
        }
        
        return self._evaluate(params, audio_file)

    def test_word(self, text="普通话", audio_file=None):
        """测试词语评测"""
        print(f"\n=== 词语评测测试 ===")
        print(f"测试文本: {text}")
        
        params = {
            "app_id": self.app_id,
            "category": "read_word",  # 词语
            "language": "zh_cn",
            "text_encoding": "utf-8",
            "result_level": "complete",
            "plev": 0,
            "text": text
        }
        
        return self._evaluate(params, audio_file)

    def test_sentence(self, text="我爱学习普通话", audio_file=None):
        """测试句子评测"""
        print(f"\n=== 句子评测测试 ===")
        print(f"测试文本: {text}")
        
        params = {
            "app_id": self.app_id,
            "category": "read_sentence",  # 句子
            "language": "zh_cn",
            "text_encoding": "utf-8",
            "result_level": "complete",
            "plev": 0,
            "text": text
        }
        
        return self._evaluate(params, audio_file)

    def test_chapter(self, text=None, audio_file=None):
        """测试篇章评测"""
        if text is None:
            text = "春天来了，大地复苏，万物生长。小草从土里钻出来，嫩嫩的，绿绿的。"
        
        print(f"\n=== 篇章评测测试 ===")
        print(f"测试文本: {text}")
        
        params = {
            "app_id": self.app_id,
            "category": "read_chapter",  # 篇章
            "language": "zh_cn",
            "text_encoding": "utf-8",
            "result_level": "complete",
            "plev": 0,
            "text": text
        }
        
        return self._evaluate(params, audio_file)

    def _evaluate(self, params, audio_file=None):
        """执行评测"""
        self.result = None
        self.error = None
        
        def on_message(ws, message):
            try:
                data = json.loads(message)
                if data.get('code') == 0:
                    self.result = data.get('data')
                    print("评测成功!")
                else:
                    self.error = f"评测失败: {data.get('message', '未知错误')}"
                    print(self.error)
            except Exception as e:
                self.error = f"解析结果失败: {str(e)}"
                print(self.error)
            ws.close()

        def on_error(ws, error):
            self.error = f"WebSocket错误: {str(error)}"
            print(self.error)

        def on_close(ws, close_status_code, close_msg):
            print("连接已关闭")

        def on_open(ws):
            def run():
                try:
                    # 发送参数
                    ws.send(json.dumps(params))
                    
                    # 发送音频数据
                    if audio_file:
                        with open(audio_file, 'rb') as f:
                            audio_data = f.read()
                    else:
                        # 生成模拟音频数据（实际使用时应该是真实录音）
                        audio_data = self._generate_mock_audio()
                    
                    # 分块发送音频
                    chunk_size = 1280
                    for i in range(0, len(audio_data), chunk_size):
                        chunk = audio_data[i:i + chunk_size]
                        ws.send(chunk, websocket.ABNF.OPCODE_BINARY)
                        time.sleep(0.01)
                    
                    # 发送结束标志
                    ws.send(b'', websocket.ABNF.OPCODE_BINARY)
                    
                except Exception as e:
                    self.error = f"发送数据失败: {str(e)}"
                    print(self.error)
                    ws.close()
            
            thread = threading.Thread(target=run)
            thread.start()

        # 创建WebSocket连接
        url = self.create_url()
        ws = websocket.WebSocketApp(url,
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_close=on_close,
                                  on_open=on_open)
        
        ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})
        
        return self.result, self.error

    def _generate_mock_audio(self):
        """生成模拟音频数据（用于测试）"""
        # 这里生成一个简单的PCM音频数据
        # 实际使用时应该替换为真实的录音数据
        sample_rate = 16000
        duration = 2  # 2秒
        samples = sample_rate * duration
        
        import numpy as np
        # 生成简单的正弦波
        t = np.linspace(0, duration, samples)
        audio = np.sin(2 * np.pi * 440 * t) * 0.3  # 440Hz正弦波
        audio = (audio * 32767).astype(np.int16)
        
        return audio.tobytes()

    def analyze_result(self, result):
        """分析评测结果"""
        if not result:
            print("没有评测结果")
            return
        
        print("\n=== 评测结果分析 ===")
        
        # 解析结果
        if 'data' in result:
            data = json.loads(result['data'])
            
            # 总分
            if 'total_score' in data:
                print(f"总分: {data['total_score']}")
            
            # 声韵分
            if 'phone_score' in data:
                print(f"声韵分: {data['phone_score']}")
            
            # 调型分
            if 'tone_score' in data:
                print(f"调型分: {data['tone_score']}")
            
            # 完整度分
            if 'integrity_score' in data:
                print(f"完整度分: {data['integrity_score']}")
            
            # 流畅度分
            if 'fluency_score' in data:
                print(f"流畅度分: {data['fluency_score']}")
            
            # 详细结果
            if 'words' in data:
                print("\n=== 详细分析 ===")
                for word in data['words']:
                    print(f"字词: {word.get('content', '')}")
                    print(f"  总分: {word.get('total_score', 0)}")
                    if 'syllables' in word:
                        for syll in word['syllables']:
                            print(f"  音节: {syll.get('content', '')}")
                            print(f"    声母: {syll.get('initial_score', 0)}")
                            print(f"    韵母: {syll.get('final_score', 0)}")
                            print(f"    声调: {syll.get('tone_score', 0)}")

def main():
    """主测试函数"""
    # 配置您的API密钥
    APP_ID = "your_app_id"  # 替换为您的APPID
    API_SECRET = "your_api_secret"  # 替换为您的API Secret
    API_KEY = "your_api_key"  # 替换为您的API Key
    
    if APP_ID == "your_app_id":
        print("请先配置您的科大讯飞API密钥!")
        print("1. 注册科大讯飞开放平台账号: https://www.xfyun.cn/")
        print("2. 创建语音评测应用")
        print("3. 获取APPID、API Secret、API Key")
        print("4. 修改本脚本中的配置信息")
        return
    
    # 创建评测器
    evaluator = XunfeiSpeechEvaluator(APP_ID, API_SECRET, API_KEY)
    
    print("科大讯飞普通话评测API测试")
    print("=" * 50)
    
    # 测试四种题型
    try:
        # 1. 单字评测
        result, error = evaluator.test_single_word("中")
        if result:
            evaluator.analyze_result(result)
        elif error:
            print(f"单字评测失败: {error}")
        
        time.sleep(1)
        
        # 2. 词语评测
        result, error = evaluator.test_word("普通话")
        if result:
            evaluator.analyze_result(result)
        elif error:
            print(f"词语评测失败: {error}")
        
        time.sleep(1)
        
        # 3. 句子评测
        result, error = evaluator.test_sentence("我爱学习普通话")
        if result:
            evaluator.analyze_result(result)
        elif error:
            print(f"句子评测失败: {error}")
        
        time.sleep(1)
        
        # 4. 篇章评测
        result, error = evaluator.test_chapter()
        if result:
            evaluator.analyze_result(result)
        elif error:
            print(f"篇章评测失败: {error}")
            
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
